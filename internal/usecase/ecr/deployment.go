package ecr

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	kubeerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"

	"api-server/internal/dto"
	"api-server/internal/entities"
	"api-server/internal/enums"
	repository "api-server/internal/repositories"
	"api-server/internal/types"
	"api-server/internal/usecase"
	"api-server/internal/utils"
	"api-server/pkg/oteltrace"
)

const (
	customImageNamespace = "custom-image" // Namespace for custom image deployments
)

// DeployToKubernetesInput contains the information needed to deploy an image to Kubernetes
type DeployToKubernetesInput struct {
	// Namespace is the Kubernetes namespace to deploy to
	Namespace string

	// Name is the name of the deployment, also used for service name
	Name string

	// ImageURI is the full URI of the ECR image to deploy
	ImageURI string

	// Port is the container port to expose
	Port int32

	// Env contains environment variables to set in the deployment
	Env map[string]string

	// Resources defines CPU and memory requests and limits
	Resources KubernetesResources

	// CreateService determines if a service should be created for the deployment
	CreateService bool

	// ServiceType is the type of Kubernetes service to create (ClusterIP, NodePort, LoadBalancer)
	ServiceType string

	// NodeName is the name of the node to deploy to (required for GPU deployments)
	NodeName *string

	ResourceName string

	// IngressConfig contains configuration for the Kubernetes Ingress resource
	IngressConfig EcrIngressConfig
}

type EcrIngressConfig struct {
	ProxyBodySize string `example:"100m"` // INFO: for 413 Request Entity Too Large HTTP error
}

// KubernetesResources contains resource requests and limits
type KubernetesResources struct {
	RequestsCPU    string
	RequestsMemory string
	LimitsCPU      string
	LimitsMemory   string
}

// KubernetesDeploymentResult contains information about the deployed resources
type KubernetesDeploymentResult struct {
	Namespace        string
	DeploymentName   string
	ServiceName      string
	ServiceType      string
	ServiceClusterIP string
	ServicePorts     []int32
}

type CreateECRDeploymentInput struct {
	Name     string
	ImageURI string
	NodeName string
	Port     int32
	Env      map[string]string
	// ProxyBodySize *int // in megabytes

	OwnerID   uuid.UUID
	OwnerType string
}

// Create a new ECR image deployment with the given parameters
// nolint: cyclop
func (u *impl) CreateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input CreateECRDeploymentInput) (*dto.ECRDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.CreateECRDeployment")
	defer span.End()

	span.AddEvent("Deploying ECR image", trace.WithAttributes(
		attribute.String("name", input.Name),
		attribute.String("image_uri", input.ImageURI),
		attribute.String("node_name", input.NodeName),
		attribute.Int("port", int(input.Port)),
	))

	// Validate that the ECR image exists
	err := u.ValidateECRImageExists(ctx, input.ImageURI)
	if err != nil {
		span.SetStatus(codes.Error, "ECR image validation failed")
		span.RecordError(err)
		return nil, err
	}

	// check deloyment name uniqueness
	deployment, err := u.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		DeploymentName: &input.Name,
	})
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		span.SetStatus(codes.Error, "failed to create deployment")
		span.RecordError(err)
		return nil, err
	}
	if deployment != nil {
		return nil, usecase.ErrDeploymentNameAlreadyExists
	}

	// prepare input
	nodeName, gpuModel, err := u.prepareNode(ctx, &input)
	if err != nil {
		span.AddEvent("failed to get node", trace.WithAttributes(attribute.String("node_name", *nodeName)))
		span.SetStatus(codes.Error, "failed to get node")
		span.RecordError(err)
		if apierrors.IsNotFound(err) {
			return nil, usecase.ErrNodeNotExist
		}
		return nil, err
	}

	// check userID or orgID
	var userID, orgID *uuid.UUID
	switch input.OwnerType {
	case "user":
		if currentUserID != input.OwnerID {
			return nil, usecase.ErrNoPermission
		}
		userID = &currentUserID
	case "org":
	// TODO: find org by ID and check if user is member and has permission
	default:
		return nil, fmt.Errorf("invalid id type: %s", input.OwnerType)
	}

	deploymentInput := repository.CreateCustomImageDeploymentInput{
		UserID:         userID,
		OrgID:          orgID,
		DeploymentName: utils.Slugify(input.Name),
		ImageURI:       input.ImageURI,
		NodeName:       input.NodeName,
		Port:           input.Port,
		Env:            input.Env,
		CPU:            1,
		Mem:            512, // in Mebibytes
		GPUModel:       gpuModel,
		ProxyBodySize:  100,
		Namespace:      customImageNamespace,
	}
	var result dto.ECRDeployment
	result.Status = "Not Running"
	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		created, err := u.repo.CreateECRDeployment(ctx, deploymentInput)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create deployment")
			span.RecordError(err)
			return fmt.Errorf("failed to create deployment: %w", err)
		}
		result = result.FromEntity(*created)
		result.URL = getDeploymentURL(created.DeploymentName, u.config.Space.SpaceDomain)

		return nil
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to create deployment")
		span.RecordError(err)
		return nil, err
	}

	return &result, nil
}

func (u *impl) prepareNode(ctx context.Context, input *CreateECRDeploymentInput) (*string, *string, error) {
	var nodeName, gpuModel *string
	nodeName = &input.NodeName
	if strings.ToLower(input.NodeName) != enums.CPU {
		node, err := u.k8sClient.CoreV1().Nodes().Get(ctx, *nodeName, metav1.GetOptions{})
		if err != nil {
			if apierrors.IsNotFound(err) {
				return nil, nil, usecase.ErrNodeNotExist
			}
			return nil, nil, err
		}
		gpuModel = utils.Ptr(utils.TrimNodeName(node.Labels[usecase.GPULabelKey]))
	}
	return nodeName, gpuModel, nil
}

func ecrResourceName(name string) string {
	return fmt.Sprintf("ecr-%s", name)
}

// DeployToKubernetes deploys an ECR image to a Kubernetes cluster
func (u *impl) DeployToKubernetes(ctx context.Context, input DeployToKubernetesInput) (*KubernetesDeploymentResult, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeployToKubernetes")
	defer span.End()

	span.AddEvent("Deploying to Kubernetes", trace.WithAttributes(
		attribute.String("namespace", input.Namespace),
		attribute.String("deployment_name", input.Name),
		attribute.String("image_uri", input.ImageURI),
	))

	// Create a deployment
	deployment, err := u.createK8sDeployment(ctx, input)
	if err != nil {
		span.SetStatus(codes.Error, "failed to create deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to create deployment: %w", err)
	}

	result := &KubernetesDeploymentResult{
		Namespace:      input.Namespace,
		DeploymentName: deployment.Name,
	}

	err = u.createKubernetesIngressResource(
		ctx,
		customImageNamespace,
		input.Name,
		u.config.Space.SpaceDomain,
		input.ResourceName,
		input.IngressConfig.ProxyBodySize,
	)
	if err != nil {
		span.SetStatus(codes.Error, "failed to Kubernetes ingress for ECR deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to Kubernetes ingress for ECR deployment: %w", err)
	}

	// Create a service if requested
	if input.CreateService {
		service, err := u.createK8sService(ctx, input)
		if err != nil {
			span.SetStatus(codes.Error, "failed to create service")
			span.RecordError(err)
			return nil, fmt.Errorf("failed to create service: %w", err)
		}

		result.ServiceName = service.Name
		result.ServiceType = string(service.Spec.Type)
		result.ServiceClusterIP = service.Spec.ClusterIP

		// Add service ports to result
		for _, port := range service.Spec.Ports {
			result.ServicePorts = append(result.ServicePorts, port.Port)
		}
	}

	return result, nil
}

// createDeployment creates a Kubernetes deployment for the given input
// nolint: cyclop, funlen
func (u *impl) createK8sDeployment(ctx context.Context, input DeployToKubernetesInput) (*appsv1.Deployment, error) {
	// Create container definition
	container := corev1.Container{
		Name:  input.ResourceName,
		Image: input.ImageURI,
		Ports: []corev1.ContainerPort{
			{
				ContainerPort: input.Port,
			},
		},
		Resources: corev1.ResourceRequirements{
			Limits:   corev1.ResourceList{},
			Requests: corev1.ResourceList{},
		},
	}

	// Add environment variables if provided
	if len(input.Env) > 0 {
		for key, value := range input.Env {
			container.Env = append(container.Env, corev1.EnvVar{
				Name:  key,
				Value: value,
			})
		}
	}

	isGpuDeployment := false // check if GPU deployment or CPU-only deployment
	if input.NodeName != nil && strings.ToLower(*input.NodeName) != enums.CPU {
		isGpuDeployment = true
	}
	if isGpuDeployment {
		gpuReq, err := resource.ParseQuantity("1")
		if err != nil {
			return nil, fmt.Errorf("invalid GPU request: %w", err)
		}

		// request GPU and CPU for GPU deployments
		cpuReq, err := resource.ParseQuantity(input.Resources.RequestsCPU)
		if err != nil {
			return nil, fmt.Errorf("invalid CPU request: %w", err)
		}
		container.Resources.Requests[corev1.ResourceCPU] = cpuReq
		container.Resources.Requests[enums.NvidiaGPUResourceName] = gpuReq

		// only limit GPU for GPU deployments
		container.Resources.Limits[enums.NvidiaGPUResourceName] = gpuReq
	} else {
		// request CPU and memory for CPU-only deployments
		cpuReq, err := resource.ParseQuantity(input.Resources.RequestsCPU)
		if err != nil {
			return nil, fmt.Errorf("invalid CPU request: %w", err)
		}
		memReq, err := resource.ParseQuantity(input.Resources.RequestsMemory)
		if err != nil {
			return nil, fmt.Errorf("invalid CPU request: %w", err)
		}
		container.Resources.Requests[corev1.ResourceCPU] = cpuReq
		container.Resources.Requests[corev1.ResourceMemory] = memReq

		// only limit memory for CPU-only deployments
		memLim, err := resource.ParseQuantity(input.Resources.LimitsMemory)
		if err != nil {
			return nil, fmt.Errorf("invalid memory request: %w", err)
		}
		container.Resources.Limits[corev1.ResourceMemory] = memLim
	}

	// Create the deployment definition
	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      input.ResourceName,
			Namespace: input.Namespace,
			Labels: map[string]string{
				"app": input.ResourceName,
			},
		},
		Spec: appsv1.DeploymentSpec{
			Replicas: utils.Ptr[int32](1),
			Selector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"app": input.ResourceName,
				},
			},
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: map[string]string{
						"app": input.ResourceName,
					},
					Annotations: map[string]string{
						"timestamp": time.Now().Format(time.RFC3339),
					},
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{container},
				},
			},
		},
	}

	if isGpuDeployment {
		if input.NodeName == nil {
			return nil, usecase.ErrGPUNodeNameRequired
		}

		// add node slelection and tolerations for GPU deployments
		deployment.Spec.Template.Spec.NodeSelector = map[string]string{
			"kubernetes.io/hostname": *input.NodeName,
		}
		deployment.Spec.Template.Spec.Tolerations = []corev1.Toleration{
			{
				Key:      enums.NvidiaGPUResourceName,
				Operator: corev1.TolerationOpExists,
				Effect:   corev1.TaintEffectNoSchedule,
			},
		}
	}

	// Create or update the deployment
	existingDeployment, err := u.k8sClient.AppsV1().Deployments(input.Namespace).Get(ctx, input.ResourceName, metav1.GetOptions{})
	if err == nil {
		// Deployment exists, update it
		deployment.ResourceVersion = existingDeployment.ResourceVersion
		return u.k8sClient.AppsV1().Deployments(input.Namespace).Update(ctx, deployment, metav1.UpdateOptions{})
	}

	// Deployment doesn't exist, create it
	return u.k8sClient.AppsV1().Deployments(input.Namespace).Create(ctx, deployment, metav1.CreateOptions{})
}

// createService creates a Kubernetes service for the given input
func (u *impl) createK8sService(ctx context.Context, input DeployToKubernetesInput) (*corev1.Service, error) {
	// Determine service type
	var serviceType corev1.ServiceType
	switch input.ServiceType {
	case "NodePort":
		serviceType = corev1.ServiceTypeNodePort
	case "LoadBalancer":
		serviceType = corev1.ServiceTypeLoadBalancer
	default:
		serviceType = corev1.ServiceTypeClusterIP
	}

	// Create the service definition
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      input.Name,
			Namespace: input.Namespace,
			Labels: map[string]string{
				"app": input.ResourceName,
			},
		},
		Spec: corev1.ServiceSpec{
			Type: serviceType,
			Selector: map[string]string{
				"app": input.ResourceName,
			},
			Ports: []corev1.ServicePort{
				{
					Port:       80,
					TargetPort: intstr.FromInt(int(input.Port)),
				},
			},
		},
	}

	// Create or update the service
	existingService, err := u.k8sClient.CoreV1().Services(input.Namespace).Get(ctx, input.Name, metav1.GetOptions{})
	if err == nil {
		// Service exists, update it
		service.ResourceVersion = existingService.ResourceVersion
		return u.k8sClient.CoreV1().Services(input.Namespace).Update(ctx, service, metav1.UpdateOptions{})
	}

	// Service doesn't exist, create it
	return u.k8sClient.CoreV1().Services(input.Namespace).Create(ctx, service, metav1.CreateOptions{})
}

func (i *impl) createKubernetesIngressResource(
	ctx context.Context,
	namespace, deploymentName, domain, resourceName string,
	proxyBodySize string,
) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.createKubernetesIngressResource")
	defer span.End()

	ingressClient := i.k8sClient.NetworkingV1().Ingresses(namespace)
	_, err := ingressClient.Get(ctx, resourceName, metav1.GetOptions{})
	if err != nil {
		if !kubeerrors.IsNotFound(err) {
			span.SetStatus(codes.Error, "failed to get Ingress resource")
			span.RecordError(err)
			return usecase.ErrGetSpaceIngressResource
		}

		span.AddEvent("Ingress for this custom image does not exist, create a new Ingress")
		ingress := &networkingv1.Ingress{
			ObjectMeta: metav1.ObjectMeta{
				Name:      resourceName,
				Namespace: namespace,
				Annotations: map[string]string{
					"nginx.ingress.kubernetes.io/proxy-body-size": proxyBodySize, // INFO: for 413 Request Entity Too Large HTTP error
				},
			},
			Spec: networkingv1.IngressSpec{
				IngressClassName: &i.config.Space.SpaceIngressClassName,

				Rules: []networkingv1.IngressRule{
					{
						Host: getDeploymentURL(deploymentName, domain),
						IngressRuleValue: networkingv1.IngressRuleValue{
							HTTP: &networkingv1.HTTPIngressRuleValue{
								Paths: []networkingv1.HTTPIngressPath{
									{
										Path:     "/",
										PathType: func() *networkingv1.PathType { pt := networkingv1.PathTypePrefix; return &pt }(),
										Backend: networkingv1.IngressBackend{
											Service: &networkingv1.IngressServiceBackend{
												Name: deploymentName,
												Port: networkingv1.ServiceBackendPort{
													Number: 80,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		}
		_, err = ingressClient.Create(ctx, ingress, metav1.CreateOptions{})
		if err != nil {
			span.SetStatus(codes.Error, "failed to create Kubernetes Ingress resource")
			span.RecordError(err)
			return usecase.ErrCreateSpaceIngressResource
		}
	} else {
		span.AddEvent("Ingress for this ECR deployment already existed")
	}

	return nil
}

func getDeploymentURL(deploymentName, domain string) string {
	return fmt.Sprintf("ecr-%s.%s", deploymentName, domain)
}

// // DeleteKubernetesDeployment deletes a Kubernetes deployment and its associated service
// func (u *impl) DeleteKubernetesDeployment(ctx context.Context, namespace, name string) error {
// 	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeleteKubernetesDeployment")
// 	defer span.End()

// 	// Check if Kubernetes client is initialized
// 	if u.k8sClient == nil {
// 		span.SetStatus(codes.Error, "kubernetes client not initialized")
// 		return errors.New("kubernetes client not initialized - must be running in-cluster")
// 	}

// 	// Delete the deployment
// 	err := u.k8sClient.AppsV1().Deployments(namespace).Delete(ctx, name, metav1.DeleteOptions{})
// 	if err != nil {
// 		span.SetStatus(codes.Error, "failed to delete deployment")
// 		span.RecordError(err)
// 		return fmt.Errorf("failed to delete deployment: %w", err)
// 	}

// 	// Try to delete the service (even if it doesn't exist)
// 	_ = u.k8sClient.CoreV1().Services(namespace).Delete(ctx, name, metav1.DeleteOptions{})

// 	return nil
// }

type UpdateECRDeploymentInput struct {
	DeploymentID  uuid.UUID
	Port          *int32
	NodeName      *string
	NumCpu        *uint
	Mem           *types.HardwareMem
	ProxyBodySize *int // in megabytes
}

// UpdateECRDeployment
// nolint: cyclop
func (u *impl) UpdateECRDeployment(ctx context.Context, currentUserID uuid.UUID, input UpdateECRDeploymentInput) (*dto.ECRDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.UpdateECRDeployment")
	defer span.End()

	var nodeName, gpuModel *string
	if input.NodeName != nil && strings.ToLower(*input.NodeName) != enums.CPU {
		nodeName = input.NodeName
		node, err := u.k8sClient.CoreV1().Nodes().Get(ctx, *nodeName, metav1.GetOptions{})
		if err != nil {
			span.AddEvent("failed to get node", trace.WithAttributes(attribute.String("node_name", *nodeName)))
			span.SetStatus(codes.Error, "failed to get node")
			span.RecordError(err)
			if apierrors.IsNotFound(err) {
				return nil, usecase.ErrNodeNotExist
			}
			return nil, err
		}
		gpuModel = utils.Ptr(utils.TrimNodeName(node.Labels[usecase.GPULabelKey]))
	}

	deploymentInput := repository.UpdateECRDeploymentInput{
		ID:            input.DeploymentID,
		NodeName:      input.NodeName,
		Port:          input.Port,
		GPUModel:      gpuModel,
		ProxyBodySize: input.ProxyBodySize,
	}
	if input.NumCpu != nil {
		deploymentInput.NumCpu = input.NumCpu
	}
	if input.Mem != nil {
		deploymentInput.Mem = utils.Ptr(input.Mem.ToMiB())
	}

	var deployment *entities.CustomImageDeployment
	err := u.repo.Transaction(ctx, func(ctx context.Context) error {
		updated, err := u.repo.UpdateECRDeployment(ctx, deploymentInput)
		if err != nil {
			span.SetStatus(codes.Error, "failed to update deployment")
			span.RecordError(err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return usecase.ErrNoDeployment
			}
			return fmt.Errorf("failed to update deployment: %w", err)
		}
		deployment = updated

		return nil
	})
	if err != nil || deployment == nil {
		span.SetStatus(codes.Error, "failed to update deployment")
		span.RecordError(err)
		return nil, err
	}

	var result dto.ECRDeployment
	result = result.FromEntity(*deployment)

	return &result, nil
}

func (u *impl) CreateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.CreateECRDeploymentEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.CreateECRDeploymentEnv")
	defer span.End()

	if err := u.repo.Transaction(ctx, func(ctx context.Context) error {
		return u.repo.CreateECRDeploymentEnv(ctx, repository.CreateECRDeploymentEnvInput{
			ID:    input.ID,
			Key:   input.Key,
			Value: input.Value,
		})
	}); err != nil {
		span.SetStatus(codes.Error, "failed to create ecr deployment env")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	return nil
}

func (u *impl) UpdateECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.UpdateECRDeploymentEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.UpdateECRDeploymentEnv")
	defer span.End()

	if err := u.repo.Transaction(ctx, func(ctx context.Context) error {
		return u.repo.UpdateECRDeploymentEnv(ctx, repository.UpdateECRDeploymentEnvInput{
			ID:     input.ID,
			OldKey: input.OldKey,
			Key:    input.Key,
			Value:  input.Value,
		})
	}); err != nil {
		span.SetStatus(codes.Error, "failed to update ecr deployment env")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	return nil
}

func (u *impl) DeleteECRDeploymentEnv(ctx context.Context, currentUserID uuid.UUID, input dto.DeleteECRDeploymentEnvInput) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeleteECRDeploymentEnv")
	defer span.End()

	if err := u.repo.Transaction(ctx, func(ctx context.Context) error {
		return u.repo.DeleteECRDeploymentEnv(ctx, repository.DeleteECRDeploymentEnv{
			ID:  input.ID,
			Key: input.Key,
		})
	}); err != nil {
		span.SetStatus(codes.Error, "failed to delete ecr deployment env")
		span.RecordError(err)
		return usecase.ErrInternal
	}

	return nil
}

func (u *impl) ListECRDeployment(ctx context.Context, currentUserID uuid.UUID, in dto.ListECRDeploymentInput) ([]dto.ECRDeployment, int64, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.ListECRDeployment")
	defer span.End()

	pagination := types.Pagination{
		PageNo:   in.Page,
		PageSize: in.PerPage,
	}
	order := types.OrderBy{
		in.OrderBy: in.OrderDirection,
	}

	deployments, total, err := u.repo.ListECRDeployment(ctx, pagination, order, repository.ListECRDeploymentInput{
		UserID:         in.UserID,
		OrgID:          in.OrgID,
		Search:         in.Search,
		DeploymentType: utils.Ptr(types.CustomImageDeploymentType_Single),
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list ECR deployments")
		span.RecordError(err)
		return nil, 0, fmt.Errorf("failed to list ECR deployments: %w", err)
	}

	result := dto.FromManyEntities[entities.CustomImageDeployment, dto.ECRDeployment](deployments)

	g, gCtx := errgroup.WithContext(ctx)
	g.SetLimit(8)
	for i, item := range result {
		select {
		case <-gCtx.Done():
			break
		default:
			g.Go(func() error {
				if result[i].User.Avatar != nil {
					repoImage, err := u.awsClient.GenPreSignUrl(ctx, *result[i].User.Avatar)
					if err != nil {
						span.SetStatus(codes.Error, "failed to generate presigned url for avatar")
						span.RecordError(err)
						return err
					}

					result[i].User.Avatar = &repoImage
				}

				result[i].URL = getDeploymentURL(item.DeploymentName, u.config.Space.SpaceDomain)

				status, err := QueryDeploymentStatus(ctx, u.k8sClient, item.ID)
				if err != nil {
					span.SetStatus(codes.Error, "failed to query deployment status from k8s cluster")
					span.RecordError(err)
					result[i].Status = ""
				} else {
					result[i].Status = status.Status
				}

				return nil
			})
		}
	}
	if err := g.Wait(); err != nil {
		span.SetStatus(codes.Error, "failed to get status for deployments")
		span.RecordError(err)
		return nil, 0, err
	}

	return result, total, nil
}

func (u *impl) GetECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*dto.ECRDeployment, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.GetECRDeployment")
	defer span.End()

	deployment, err := u.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrNoDeployment
		}
		return nil, fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	// Check if user has access to the deployment
	// if deployment.UserID != nil && *deployment.UserID != currentUserID {
	// 	return nil, usecase.ErrNoPermission
	// }

	status, err := QueryDeploymentStatus(ctx, u.k8sClient, deployment.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to query deployment status from k8s cluster")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to query deployment status from k8s cluster: %w", err)
	}

	var result dto.ECRDeployment
	result = result.FromEntity(*deployment)
	result.URL = getDeploymentURL(deployment.DeploymentName, u.config.Space.SpaceDomain)
	if deployment.Namespace != nil {
		result.PrivateURL = utils.GetPrivateDeploymentURL(deployment.DeploymentName, *deployment.Namespace)
	}
	result.Status = status.Status

	// sort the env variables
	var envMap map[string]string
	_ = json.Unmarshal(deployment.Env, &envMap)
	envs := []dto.EnvKV{}
	for k, v := range envMap {
		envs = append(envs, dto.EnvKV{
			Key:   k,
			Value: v,
		})
	}
	// Sort by Key alphabetically
	sort.Slice(envs, func(i, j int) bool {
		return envs[i].Key < envs[j].Key
	})
	result.Env = envs

	return &result, nil
}

func (u *impl) DeployECR(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) (*KubernetesDeploymentResult, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeployECR")
	defer span.End()

	// Get the deployment details
	deployment, err := u.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrNoDeployment
		}
		return nil, fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	// Validate that the ECR image exists
	err = u.ValidateECRImageExists(ctx, deployment.ImageURI)
	if err != nil {
		span.SetStatus(codes.Error, "ECR image validation failed")
		span.RecordError(err)
		return nil, err
	}

	// Check if user has access to deploy
	// if deployment.UserID != nil && *deployment.UserID != currentUserID {
	// 	return nil, usecase.ErrNoPermission
	// }

	resourceName := ecrResourceName(deployment.ID.String())

	// clean existing Kubernetes deployment
	err = u.deleteKubeResource(ctx, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete K8s resource")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to delete K8s resource: %w", err)
	}

	var env map[string]string
	if err := json.Unmarshal(deployment.Env, &env); err != nil {
		span.SetStatus(codes.Error, "failed to unmarshal environment variables")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to unmarshal environment variables: %w", err)
	}

	// Prepare node name if needed
	var nodeName *string
	if strings.ToLower(deployment.NodeName) != enums.CPU {
		nodeName = &deployment.NodeName
	}

	// Deploy to Kubernetes
	result, err := u.DeployToKubernetes(ctx, DeployToKubernetesInput{
		Namespace:     customImageNamespace,
		Name:          deployment.DeploymentName,
		ImageURI:      deployment.ImageURI,
		Port:          deployment.Port,
		Env:           env,
		CreateService: true,
		ServiceType:   "ClusterIP",
		NodeName:      nodeName,
		Resources: KubernetesResources{
			RequestsCPU:    strconv.Itoa(deployment.CPU),
			RequestsMemory: types.HardwareMem{}.FromMiB(uint(deployment.Mem)).ToK8SHardwareMem().String(),
			LimitsCPU:      strconv.Itoa(deployment.CPU),
			LimitsMemory:   types.HardwareMem{}.FromMiB(uint(deployment.Mem)).ToK8SHardwareMem().String(),
		},
		ResourceName: resourceName,
		IngressConfig: EcrIngressConfig{
			ProxyBodySize: fmt.Sprintf("%dm", deployment.ProxyBodySize),
		},
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to deploy to Kubernetes cluster")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to deploy to Kubernetes cluster: %w", err)
	}

	return result, nil
}

func (u *impl) DeleteECRDeployment(ctx context.Context, currentUserID uuid.UUID, deploymentID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.DeleteECRDeployment")
	defer span.End()

	// Get the deployment details
	deployment, err := u.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrNoDeployment
		}
		return fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	// Delete Kubernetes resources within a transaction
	err = u.repo.Transaction(ctx, func(ctx context.Context) error {
		resourceName := ecrResourceName(deployment.ID.String())

		err := u.deleteKubeResource(ctx, resourceName)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete K8s resource")
			span.RecordError(err)
			return fmt.Errorf("failed to delete K8s resource: %w", err)
		}

		// Delete deployment record from database
		err = u.repo.DeleteECRDeployment(ctx, deploymentID)
		if err != nil {
			span.SetStatus(codes.Error, "failed to delete ECR deployment record")
			span.RecordError(err)
			return fmt.Errorf("failed to delete ECR deployment record: %w", err)
		}

		return nil
	})

	if err != nil {
		span.SetStatus(codes.Error, "failed to delete ECR deployment")
		span.RecordError(err)
		return err
	}

	return nil
}

func (u *impl) StopECRDeployment(ctx context.Context, deploymentID uuid.UUID) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.StopECRDeployment")
	defer span.End()

	// Get the deployment details
	deployment, err := u.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return usecase.ErrNoDeployment
		}
		return fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	// Stop Kubernetes resources (but keep database record)
	resourceName := ecrResourceName(deployment.ID.String())
	err = u.deleteKubeResource(ctx, resourceName)
	if err != nil {
		span.SetStatus(codes.Error, "failed to delete K8s resource")
		span.RecordError(err)
		return fmt.Errorf("failed to delete K8s resource: %w", err)
	}

	span.AddEvent("ECR deployment stopped successfully", trace.WithAttributes(
		attribute.String("deployment_id", deploymentID.String()),
		attribute.String("deployment_name", deployment.DeploymentName),
	))

	return nil
}

func (u *impl) deleteKubeResource(ctx context.Context, resourceName string) error {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.deleteKubeResource")
	defer span.End()

	// Delete Ingress
	err := u.k8sClient.NetworkingV1().Ingresses(customImageNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil && !kubeerrors.IsNotFound(err) {
		span.SetStatus(codes.Error, "failed to delete Kubernetes ingress")
		span.RecordError(err)
		return fmt.Errorf("failed to delete Kubernetes ingress: %w", err)
	}

	// Delete Service
	err = u.k8sClient.CoreV1().Services(customImageNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil && !kubeerrors.IsNotFound(err) {
		span.SetStatus(codes.Error, "failed to delete Kubernetes service")
		span.RecordError(err)
		return fmt.Errorf("failed to delete Kubernetes service: %w", err)
	}

	// Delete Deployment
	err = u.k8sClient.AppsV1().Deployments(customImageNamespace).Delete(ctx, resourceName, metav1.DeleteOptions{})
	if err != nil && !kubeerrors.IsNotFound(err) {
		span.SetStatus(codes.Error, "failed to delete Kubernetes deployment")
		span.RecordError(err)
		return fmt.Errorf("failed to delete Kubernetes deployment: %w", err)
	}

	return nil
}

func (i *impl) GetDeploymentStatus(ctx context.Context, deploymentID uuid.UUID) (*dto.GetDeploymentStatusResponse, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.GetDeploymentStatus")
	defer span.End()

	deployment, err := i.repo.FindECRDeployment(ctx, repository.FindECRDeploymentInput{
		ID: &deploymentID,
	})
	if err != nil {
		span.SetStatus(codes.Error, "failed to find ECR deployment")
		span.RecordError(err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, usecase.ErrNoDeployment
		}
		return nil, fmt.Errorf("failed to find ECR deployment: %w", err)
	}

	status, err := QueryDeploymentStatus(ctx, i.k8sClient, deployment.ID)
	if err != nil {
		span.SetStatus(codes.Error, "failed to query deployment status")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to query deployment status: %w", err)
	}

	return &dto.GetDeploymentStatusResponse{
		Data: status,
	}, nil
}

func QueryDeploymentStatus(ctx context.Context, k8sClient kubernetes.Interface, deploymentID uuid.UUID) (*dto.DeploymentStatus, error) {
	ctx, span := oteltrace.Tracer.Start(ctx, "usecase.ecr.queryDeploymentStatus")
	defer span.End()

	resourceName := ecrResourceName(deploymentID.String())

	var status dto.DeploymentStatus
	existingDeployment, err := k8sClient.AppsV1().Deployments(customImageNamespace).Get(ctx, resourceName, metav1.GetOptions{})
	if err != nil {
		if kubeerrors.IsNotFound(err) {
			status.Status = "Not Running"
			return &status, nil
		}

		span.SetStatus(codes.Error, "failed to find k8s deployment")
		span.RecordError(err)
		return nil, fmt.Errorf("failed to find k8s deployment: %w", err)
	}
	_ = existingDeployment

	podLabelSelector := fmt.Sprintf("app=%s", resourceName)
	span.SetAttributes(attribute.String("pod_label_selector", podLabelSelector))

	podList, err := k8sClient.CoreV1().
		Pods(customImageNamespace).
		List(ctx, metav1.ListOptions{
			LabelSelector: podLabelSelector,
		})
	if err != nil {
		span.SetStatus(codes.Error, "failed to list pods")
		span.RecordError(err)
		return nil, err
	}

	// if there is no pod or container, just return Scheduling while waiting for pod to be scheduled by k8s
	if len(podList.Items) == 0 {
		status.Status = "Scheduling"
	} else {
		pod := podList.Items[0]
		containerStatuses := pod.Status.ContainerStatuses
		if len(containerStatuses) == 0 {
			status.Status = "Scheduling"
		} else {
			containerState := containerStatuses[0].State
			dto.MapContainerStatus(&containerState, &status)
		}
	}

	return &status, nil
}
