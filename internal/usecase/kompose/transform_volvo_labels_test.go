package kompose

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/yaml.v3"
)

func TestTransformVolvoLabels(t *testing.T) {
	tests := []struct {
		name                    string
		content                 string
		expectedTransformations int
		expectedLabels          []string
		expectError             bool
		errorContains           string
	}{
		{
			name: "transform volvo.service.expose to kompose labels",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
    labels:
      volvo.service.expose: true
      app: web
`,
			expectedTransformations: 1,
			expectedLabels: []string{
				"kompose.service.expose",
				"kompose.service.expose.ingress-class-name",
			},
		},
		// 		{
		// 			name: "transform volvo.service.type to kompose.service.type",
		// 			content: `
		// services:
		//   web:
		//     image: nginx:latest
		//     labels:
		//       volvo.service.type: "ClusterIP"
		//       app: web
		// `,
		// 			expectedTransformations: 1,
		// 			expectedLabels: []string{
		// 				"kompose.service.type",
		// 			},
		// 		},
		// 		{
		// 			name: "transform volvo.volume.size to kompose.volume.size",
		// 			content: `
		// services:
		//   db:
		//     image: postgres:13
		//     labels:
		//       volvo.volume.size: "5Gi"
		//       app: database
		// `,
		// 			expectedTransformations: 1,
		// 			expectedLabels: []string{
		// 				"kompose.volume.size",
		// 			},
		// 		},
		// 		{
		// 			name: "transform volvo.service.nodeport to kompose.service.type NodePort",
		// 			content: `
		// services:
		//   api:
		//     image: api:latest
		//     labels:
		//       volvo.service.nodeport: true
		// `,
		// 			expectedTransformations: 1,
		// 			expectedLabels: []string{
		// 				"kompose.service.type",
		// 			},
		// 		},
		// 		{
		// 			name: "transform multiple volvo labels in single service",
		// 			content: `
		// services:
		//   web:
		//     image: nginx:latest
		//     labels:
		//       volvo.service.expose: true
		//       volvo.service.type: "LoadBalancer"
		//       app: web
		// `,
		// 			expectedTransformations: 2,
		// 			expectedLabels: []string{
		// 				"kompose.service.expose",
		// 				"kompose.service.expose.ingress-class-name",
		// 				"kompose.service.type",
		// 			},
		// 		},
		// 		{
		// 			name: "transform volvo labels across multiple services",
		// 			content: `
		// services:
		//   web:
		//     image: nginx:latest
		//     labels:
		//       volvo.service.expose: true
		//       app: web
		//   db:
		//     image: postgres:13
		//     labels:
		//       volvo.volume.size: "10Gi"
		//       app: database
		// `,
		// 			expectedTransformations: 2,
		// 			expectedLabels: []string{
		// 				"kompose.service.expose",
		// 				"kompose.service.expose.ingress-class-name",
		// 				"kompose.volume.size",
		// 			},
		// 		},
		{
			name: "no volvo labels to transform",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      app: web
      version: "1.0"
`,
			expectedTransformations: 0,
			expectedLabels:          []string{},
		},
		{
			name: "service without labels",
			content: `
services:
  web:
    image: nginx:latest
    ports:
      - "8080:80"
`,
			expectedTransformations: 0,
			expectedLabels:          []string{},
		},
		{
			name: "invalid YAML",
			content: `
services:
  web:
    image: nginx:latest
    labels: [
`,
			expectError:   true,
			errorContains: "invalid YAML syntax",
		},
		{
			name: "transform volvo.ingress.proxy-body-size to nginx annotation",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      volvo.ingress.proxy-body-size: "100m"
      app: web
`,
			expectedTransformations: 1,
			expectedLabels: []string{
				"nginx.ingress.kubernetes.io/proxy-body-size",
			},
		},
		{
			name: "invalid proxy-body-size format",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      volvo.ingress.proxy-body-size: "invalid-format"
      app: web
`,
			expectError:   true,
			errorContains: "invalid proxy-body-size format",
		},
		{
			name: "empty proxy-body-size value",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      volvo.ingress.proxy-body-size: ""
      app: web
`,
			expectError:   true,
			errorContains: "proxy-body-size value cannot be empty",
		},
		{
			name: "missing services section",
			content: `
version: "3.8"
volumes:
  data: {}
`,
			expectError:   true,
			errorContains: "missing required 'services' section",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response, err := TransformVolvoLabels(context.Background(), TransformVolvoLabelsInput{
				DockerComposeContent: tt.content,
			})

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedTransformations, response.TransformationCount)
			assert.Len(t, response.TransformedLabels, tt.expectedTransformations)

			// Verify the transformed content is valid YAML
			var transformedConfig map[string]interface{}
			err = yaml.Unmarshal([]byte(response.TransformedContent), &transformedConfig)
			require.NoError(t, err, "Transformed content should be valid YAML")

			// Check that expected labels are present in the transformed content
			if len(tt.expectedLabels) > 0 {
				for _, expectedLabel := range tt.expectedLabels {
					assert.Contains(t, response.TransformedContent, expectedLabel,
						"Transformed content should contain label: %s", expectedLabel)
				}
			}

			// Verify that volvo labels are removed
			assert.NotContains(t, response.TransformedContent, "volvo.",
				"Transformed content should not contain any volvo labels")

			// Verify transformation details
			for _, transformed := range response.TransformedLabels {
				assert.NotEmpty(t, transformed.ServiceName)
				assert.NotEmpty(t, transformed.OriginalLabel)
				assert.NotEmpty(t, transformed.TransformedTo)
				assert.True(t, strings.HasPrefix(transformed.OriginalLabel, "volvo."))
			}
		})
	}
}

func TestTransformVolvoResourceRequirementLabels(t *testing.T) {
	content := `
services:
  web:
    image: nginx:latest
    labels:
      volvo.deployment.request.memory: "256Mi"
      volvo.deployment.request.cpu: "100m"
      volvo.deployment.limit.memory: "512Mi"
      volvo.deployment.limit.cpu: "200m"
      app: web
`

	response, err := TransformVolvoLabels(context.Background(), TransformVolvoLabelsInput{
		DockerComposeContent: content,
	})

	require.NoError(t, err)
	assert.Equal(t, 4, response.TransformationCount)
	assert.Len(t, response.TransformedLabels, 4)

	// Check that resource requirements are captured
	assert.NotNil(t, response.ResourceRequirements)
	assert.Contains(t, response.ResourceRequirements, "web")

	webResources := response.ResourceRequirements["web"]
	require.NotNil(t, webResources)

	// Check requests
	require.NotNil(t, webResources.Requests)
	assert.Equal(t, "256Mi", *webResources.Requests.Memory)
	assert.Equal(t, "100m", *webResources.Requests.CPU)

	// Check limits
	require.NotNil(t, webResources.Limits)
	assert.Equal(t, "512Mi", *webResources.Limits.Memory)
	assert.Equal(t, "200m", *webResources.Limits.CPU)

	// Verify that volvo resource labels are removed from transformed content
	assert.NotContains(t, response.TransformedContent, "volvo.deployment.request.memory")
	assert.NotContains(t, response.TransformedContent, "volvo.deployment.request.cpu")
	assert.NotContains(t, response.TransformedContent, "volvo.deployment.limit.memory")
	assert.NotContains(t, response.TransformedContent, "volvo.deployment.limit.cpu")

	// Verify that non-volvo labels are preserved
	assert.Contains(t, response.TransformedContent, "app: web")
}

func TestTransformVolvoLabelsSpecificMappings(t *testing.T) {
	// Test specific transformation mappings
	content := `
services:
  web:
    image: nginx:latest
    labels:
      volvo.service.expose: true
      # volvo.service.type: "ClusterIP"
      # volvo.volume.size: "5Gi"
      # volvo.service.nodeport: true
      # volvo.service.loadbalancer: true
      app: web
`

	response, err := TransformVolvoLabels(context.Background(), TransformVolvoLabelsInput{
		DockerComposeContent: content,
	})

	require.NoError(t, err)
	// assert.Equal(t, 5, response.TransformationCount)

	// Parse the transformed content to verify specific mappings
	var transformedConfig map[string]interface{}
	err = yaml.Unmarshal([]byte(response.TransformedContent), &transformedConfig)
	require.NoError(t, err)

	services := transformedConfig["services"].(map[string]interface{})
	webService := services["web"].(map[string]interface{})
	labels := webService["labels"].(map[string]interface{})

	// Check specific transformations
	assert.Equal(t, true, labels["kompose.service.expose"])
	assert.Equal(t, "nginx", labels["kompose.service.expose.ingress-class-name"])
	// Note: When multiple volvo labels map to the same kompose label, the last one wins
	// In this case, volvo.service.loadbalancer (which maps to LoadBalancer) comes after volvo.service.type
	// assert.Equal(t, "LoadBalancer", labels["kompose.service.type"]) // Last transformation wins
	// assert.Equal(t, "5Gi", labels["kompose.volume.size"])           // Should preserve original value

	// Check that original app label is preserved
	assert.Equal(t, "web", labels["app"])

	// Verify no volvo labels remain
	for labelKey := range labels {
		assert.False(t, strings.HasPrefix(labelKey, "volvo."),
			"No volvo labels should remain, found: %s", labelKey)
	}
}

func TestValidateProxyBodySize(t *testing.T) {
	tests := []struct {
		name        string
		value       string
		expectError bool
	}{
		{
			name:        "valid format with m suffix",
			value:       "100m",
			expectError: false,
		},
		{
			name:        "valid format with M suffix",
			value:       "100M",
			expectError: false,
		},
		{
			name:        "valid format with g suffix",
			value:       "1g",
			expectError: false,
		},
		{
			name:        "valid format with G suffix",
			value:       "2G",
			expectError: false,
		},
		{
			name:        "valid format with k suffix",
			value:       "500k",
			expectError: false,
		},
		{
			name:        "valid format with K suffix",
			value:       "1024K",
			expectError: false,
		},
		{
			name:        "valid format without suffix",
			value:       "1048576",
			expectError: false,
		},
		{
			name:        "valid zero value",
			value:       "0",
			expectError: false,
		},
		{
			name:        "invalid format with invalid suffix",
			value:       "100x",
			expectError: true,
		},
		{
			name:        "invalid format with text",
			value:       "invalid-format",
			expectError: true,
		},
		{
			name:        "invalid format with space",
			value:       "100 m",
			expectError: true,
		},
		{
			name:        "empty value",
			value:       "",
			expectError: true,
		},
		{
			name:        "invalid format with decimal",
			value:       "100.5m",
			expectError: true,
		},
		{
			name:        "invalid format with negative",
			value:       "-100m",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateProxyBodySize(tt.value)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestTransformVolvoGPULabels(t *testing.T) {
	tests := []struct {
		name                    string
		content                 string
		expectedTransformations int
		expectedGPUEnabled      bool
		expectedGPUType         *string
		expectError             bool
		errorContains           string
	}{
		{
			name: "transform volvo.gpu: true to GPU requirements",
			content: `
services:
  web:
    image: nvidia/cuda:latest
    labels:
      volvo.gpu: true
      app: web
`,
			expectedTransformations: 1,
			expectedGPUEnabled:      true,
			expectedGPUType:         nil,
		},
		{
			name: "transform volvo.gpu: Tesla-T4-16GB to specific GPU requirements",
			content: `
services:
  web:
    image: nvidia/cuda:latest
    labels:
      volvo.gpu: Tesla-T4-16GB
      app: web
`,
			expectedTransformations: 1,
			expectedGPUEnabled:      true,
			expectedGPUType:         stringPtr("Tesla-T4-16GB"),
		},
		{
			name: "transform volvo.gpu: false to disable GPU",
			content: `
services:
  web:
    image: nginx:latest
    labels:
      volvo.gpu: false
      app: web
`,
			expectedTransformations: 1,
			expectedGPUEnabled:      false,
			expectedGPUType:         nil,
		},
		{
			name: "invalid GPU type format",
			content: `
services:
  web:
    image: nvidia/cuda:latest
    labels:
      volvo.gpu: "invalid@gpu#type"
      app: web
`,
			expectError:   true,
			errorContains: "invalid GPU type format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			response, err := TransformVolvoLabels(context.Background(), TransformVolvoLabelsInput{
				DockerComposeContent: tt.content,
			})

			if tt.expectError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorContains)
				return
			}

			require.NoError(t, err)
			assert.Equal(t, tt.expectedTransformations, response.TransformationCount)

			// Check GPU requirements
			assert.NotNil(t, response.GPURequirements)
			assert.Contains(t, response.GPURequirements, "web")

			webGPU := response.GPURequirements["web"]
			require.NotNil(t, webGPU)
			assert.Equal(t, tt.expectedGPUEnabled, webGPU.Enabled)

			if tt.expectedGPUType != nil {
				require.NotNil(t, webGPU.GPUType)
				assert.Equal(t, *tt.expectedGPUType, *webGPU.GPUType)
			} else {
				assert.Nil(t, webGPU.GPUType)
			}

			// Check tolerations and node selector for enabled GPU
			if tt.expectedGPUEnabled {
				assert.NotEmpty(t, webGPU.Tolerations)
				assert.Equal(t, "nvidia.com/gpu", webGPU.Tolerations[0].Key)
				assert.Equal(t, "Exists", webGPU.Tolerations[0].Operator)
				assert.Equal(t, "NoSchedule", webGPU.Tolerations[0].Effect)

				// assert.NotEmpty(t, webGPU.NodeSelector)
				if tt.expectedGPUType != nil {
					assert.Equal(t, *tt.expectedGPUType, webGPU.NodeSelector["nvidia.com/gpu.product"])
				}

				// Check that GPU resources are added to ResourceRequirements
				assert.NotNil(t, response.ResourceRequirements)
				assert.Contains(t, response.ResourceRequirements, "web")
				webResources := response.ResourceRequirements["web"]
				require.NotNil(t, webResources)
				require.NotNil(t, webResources.Requests)
				require.NotNil(t, webResources.Requests.GPU)
				assert.Equal(t, "1", *webResources.Requests.GPU)
				require.NotNil(t, webResources.Limits)
				require.NotNil(t, webResources.Limits.GPU)
				assert.Equal(t, "1", *webResources.Limits.GPU)
			}

			// Verify the transformed content doesn't contain the original volvo.gpu label
			var transformedConfig map[string]interface{}
			err = yaml.Unmarshal([]byte(response.TransformedContent), &transformedConfig)
			require.NoError(t, err)

			services := transformedConfig["services"].(map[string]interface{})
			webService := services["web"].(map[string]interface{})
			if labels, hasLabels := webService["labels"]; hasLabels {
				labelMap := labels.(map[string]interface{})
				_, hasVolvoGPU := labelMap["volvo.gpu"]
				assert.False(t, hasVolvoGPU, "volvo.gpu label should be removed from transformed content")
			}
		})
	}
}

// Helper function to create string pointer
func stringPtr(s string) *string {
	return &s
}
